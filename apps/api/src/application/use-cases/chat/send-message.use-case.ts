import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';

export interface SendMessageRequest {
  message: string;
  conversationHistory?: Array<{
    role: 'user' | 'assistant';
    content: string;
  }>;
}

export interface SendMessageResponse {
  message: string;
  conversationId?: string;
}

@Injectable()
export class SendMessageUseCase {
  private openai: OpenAI;

  constructor(private configService: ConfigService) {
    const apiKey = this.configService.get<string>('OPENAI_API_KEY');
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY is not configured');
    }
    
    this.openai = new OpenAI({
      apiKey,
    });
  }

  async execute(request: SendMessageRequest): Promise<SendMessageResponse> {
    try {
      const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: `You are a helpful AI assistant for Checku, a platform that helps users with their needs. 
          Be friendly, helpful, and concise in your responses. If users ask about features or functionality, 
          guide them appropriately. Keep responses conversational and engaging.`
        },
        ...(request.conversationHistory || []).map(msg => ({
          role: msg.role as 'user' | 'assistant',
          content: msg.content,
        })),
        {
          role: 'user',
          content: request.message,
        },
      ];

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages,
        max_tokens: 500,
        temperature: 0.7,
      });

      const responseMessage = completion.choices[0]?.message?.content;
      
      if (!responseMessage) {
        throw new Error('No response from OpenAI');
      }

      return {
        message: responseMessage,
      };
    } catch (error) {
      console.error('OpenAI API Error:', error);
      throw new Error('Failed to get response from AI assistant');
    }
  }
}
