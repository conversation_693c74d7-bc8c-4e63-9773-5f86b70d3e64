"use client";

import { useState } from "react";
import { MessageCircle, X } from "lucide-react";

export function SimpleFloatingChatbot() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Chat Window */}
      {isOpen && (
        <div className="mb-4 w-80 h-96 bg-white border border-gray-300 rounded-lg shadow-lg overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-3 border-b bg-blue-600 text-white">
            <div className="flex items-center gap-2">
              <MessageCircle className="h-4 w-4" />
              <span className="font-medium text-sm">AI Assistant</span>
            </div>
            <button
              onClick={() => setIsOpen(false)}
              className="h-6 w-6 flex items-center justify-center hover:bg-blue-700 rounded"
            >
              <X className="h-3 w-3" />
            </button>
          </div>

          {/* Chat Content */}
          <div className="p-4 h-full bg-gray-50">
            <p className="text-gray-600 text-sm">
              Simple chatbot test. The full version will have OpenAI
              integration.
            </p>
          </div>
        </div>
      )}

      {/* Floating Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="h-12 w-12 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-all duration-200 hover:scale-110 active:scale-95 flex items-center justify-center"
      >
        <MessageCircle className="h-5 w-5" />
      </button>
    </div>
  );
}

export default SimpleFloatingChatbot;
