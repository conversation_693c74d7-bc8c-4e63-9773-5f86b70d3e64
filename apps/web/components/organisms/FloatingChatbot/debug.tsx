"use client";

import { MessageCircle } from "lucide-react";

export function DebugFloatingChatbot() {
  console.log("DebugFloatingChatbot is rendering");
  
  return (
    <div 
      className="fixed bottom-4 left-4 z-50 bg-red-500 text-white p-4 rounded-lg"
      style={{
        position: 'fixed',
        bottom: '16px',
        left: '16px',
        zIndex: 9999,
        backgroundColor: 'red',
        color: 'white',
        padding: '16px',
        borderRadius: '8px'
      }}
    >
      <div className="flex items-center gap-2">
        <MessageCircle className="h-6 w-6" />
        <span>Debug Chat But<PERSON></span>
      </div>
    </div>
  );
}

export default DebugFloatingChatbot;
