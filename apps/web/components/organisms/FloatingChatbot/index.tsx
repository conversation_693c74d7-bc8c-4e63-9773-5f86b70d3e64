"use client";

import { MessageCircle, X, Minimize2 } from "lucide-react";
import { Button } from "@/components/atoms/Button";
import { ChatWindow } from "@/components/molecules/ChatWindow";
import { useChatStore } from "@/store/chatStore";
import { cn } from "@/lib/utils";

interface FloatingChatbotProps {
  className?: string;
}

export function FloatingChatbot({ className }: FloatingChatbotProps) {
  const { isOpen, toggleChat, closeChat } = useChatStore();

  return (
    <div className={cn("fixed bottom-4 left-4 z-50", className)}>
      {/* Chat Window */}
      {isOpen && (
        <div className="mb-4 w-80 h-96 bg-background border border-border rounded-lg shadow-lg overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-3 border-b bg-primary text-primary-foreground">
            <div className="flex items-center gap-2">
              <MessageCircle className="h-4 w-4" />
              <span className="font-medium text-sm">AI Assistant</span>
            </div>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                onClick={closeChat}
                className="h-6 w-6 text-primary-foreground hover:bg-primary-foreground/20"
              >
                <Minimize2 className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={closeChat}
                className="h-6 w-6 text-primary-foreground hover:bg-primary-foreground/20"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
          
          {/* Chat Content */}
          <div className="h-[calc(100%-48px)]">
            <ChatWindow />
          </div>
        </div>
      )}

      {/* Floating Button */}
      <Button
        onClick={toggleChat}
        size="icon"
        className={cn(
          "h-12 w-12 rounded-full shadow-lg transition-all duration-200",
          "hover:scale-110 active:scale-95",
          isOpen ? "bg-muted text-muted-foreground" : "bg-primary text-primary-foreground"
        )}
      >
        {isOpen ? (
          <Minimize2 className="h-5 w-5" />
        ) : (
          <MessageCircle className="h-5 w-5" />
        )}
      </Button>
    </div>
  );
}

export default FloatingChatbot;
