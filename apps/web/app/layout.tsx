import type { Metada<PERSON> } from "next";
import localFont from "next/font/local";
import "./globals.css";
import { Providers } from "../providers/Providers";
import { AuthProvider } from "@/components/templates/Auth/AuthProvider";
import { FloatingChatbot } from "@/components/organisms";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
});

const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

type Props = {
  children: React.ReactNode;
  params: { locale: string };
};

// This is a root layout that wraps all pages
export default async function RootLayout({
  children,
  params,
}: Readonly<Props>) {
  // Default to 'en' if no locale is provided
  const locale = params?.locale || "en";

  return (
    <html
      lang={locale}
      suppressHydrationWarning
      className={`${geistSans.variable} ${geistMono.variable}`}
    >
      <body className="min-h-screen bg-background font-sans antialiased">
        <Providers>
          <AuthProvider>{children}</AuthProvider>
          <FloatingChatbot isOpen />
        </Providers>
      </body>
    </html>
  );
}
